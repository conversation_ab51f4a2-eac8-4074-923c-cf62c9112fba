import { NextRequest, NextResponse } from 'next/server';
import { getReports, createReport } from '@/lib/data/reports';

/**
 * GET /api/reports
 * Returns a list of reports with optional filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const limit = searchParams.get('_limit');
    const page = searchParams.get('_page');
    const sortBy = searchParams.get('_sort') || undefined;
    const order = searchParams.get('_order') as 'asc' | 'desc' | undefined;
    
    // Build query object from all other parameters
    const query: Record<string, string | string[]> = {};
    searchParams.forEach((value, key) => {
      if (!['_limit', '_page', '_sort', '_order'].includes(key)) {
        query[key] = value;
      }
    });
    
    const reports = await getReports({
      query,
      limit: limit ? parseInt(limit) : undefined,
      page: page ? parseInt(page) : undefined,
      sortBy,
      order
    });
    
    return NextResponse.json(reports);
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/reports
 * Creates a new report
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newReport = await createReport(body);
    
    return NextResponse.json(newReport, { status: 201 });
  } catch (error) {
    console.error('Error creating report:', error);
    return NextResponse.json(
      { error: 'Failed to create report' },
      { status: 500 }
    );
  }
}
