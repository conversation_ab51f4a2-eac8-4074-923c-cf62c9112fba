"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Sidebar, PanelContent } from "./components";
import { PreviewArea } from "./components/preview";
import { ReportEditorProps, ReportInfo, Section, Template, StyleOptions } from "./types";

export function ReportEditor({ reportId, templateId = 'professional', styleId, onSave, onCancel }: ReportEditorProps) {
  const router = useRouter();

  // UI State
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const [activeSectionId, setActiveSectionId] = useState<string | null>(null);
  const [activeSectionTitle, setActiveSectionTitle] = useState<string>("");
  const [pulsingSectionId, setPulsingSectionId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Report Data State
  const [reportInfo, setReportInfo] = useState<ReportInfo>({
    title: "Untitled Report",
    description: "",
    client: undefined,
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [sections, setSections] = useState<Section[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>(templateId);
  const [templateName, setTemplateName] = useState<string>("Default Template");
  const [clients, setClients] = useState<any[]>([]);
  // Initialize styles with minimal defaults to allow AI maximum creativity
  const [styles, setStyles] = useState<StyleOptions>({
    styleTemplateId: styleId || undefined
  });

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch templates
        try {
          const templateResponse = await fetch('/assets/mock-data/db.json');
          if (templateResponse.ok) {
            const data = await templateResponse.json();
            
            // Extract templates from the db.json file
            const templatesArray = data.templates || [];

            setTemplates(templatesArray);

            // Find the selected template
            const selectedTemplate = templatesArray.find((t: any) => t.id === templateId);
            if (selectedTemplate) {
              setTemplateName(selectedTemplate.name);

              // Initialize with template sections if available
              if (selectedTemplate.sections && selectedTemplate.sections.length > 0 && selectedTemplate.fields && selectedTemplate.fields.length > 0 && !reportId) {
                // Create sections from the template sections
                const templateSections = selectedTemplate.sections.map((section: any) => ({
                  id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                  title: section.title || "Untitled Section",
                  type: section.type || "title-page",
                  content: section.content || {}
                }));

                // Add fields to the content section
                const contentSection = templateSections.find((s: { title: string }) => s.title === 'Content');
                if (contentSection) {
                  selectedTemplate.fields.forEach((field: any) => {
                    // Add each field as a property in the content object
                    (contentSection.content as Record<string, string>)[field.id] = field.content || '';
                  });
                }

                setSections(templateSections);
                if (templateSections.length > 0) {
                  setActiveSectionId(templateSections[0].id);
                  setActiveSectionTitle(templateSections[0].title);
                }
              } else if (selectedTemplate.fields && selectedTemplate.fields.length > 0 && !reportId) {
              // Handle example template with fields
              const templateSections = [
                {
                  id: `section-${Date.now()}-1`,
                  title: 'Title Page',
                  type: 'title-page',
                  content: {
                    title: selectedTemplate.name,
                    subtitle: "Created with CS-Hero",
                    footer: "Confidential"
                  }
                },
                {
                  id: `section-${Date.now()}-2`,
                  title: 'Report Content',
                  type: 'content-section',
                  content: {} as Record<string, string>
                }
              ];
              
              // Add fields to the content section
              const contentSection = templateSections[1];
              selectedTemplate.fields.forEach((field: any) => {
                // Add each field as a property in the content object
                (contentSection.content as Record<string, string>)[field.id] = field.content || '';
              });

                setSections(templateSections);
                if (templateSections.length > 0) {
                  setActiveSectionId(templateSections[0].id);
                  setActiveSectionTitle(templateSections[0].title);
                }
              }
            }
          }
        } catch (error) {
          console.error('Error fetching templates:', error);

            // Create default sections if no templates are available
            if (!reportId) {
              const defaultSections = [
                {
                  id: `section-${Date.now()}-1`,
                  title: 'Title Page',
                  type: 'title-page',
                  content: {
                    title: "New Report",
                    subtitle: "Created with CS-Hero",
                    footer: "Confidential"
                  }
                },
                {
                  id: `section-${Date.now()}-2`,
                  title: 'Executive Summary',
                  type: 'content-section',
                  content: {
                    summary: 'This section provides a high-level overview of the report findings.'
                  }
                },
                {
                  id: `section-${Date.now()}-3`,
                  title: 'Key Information',
                  type: 'data-section',
                  content: {
                    heading: "Key Information",
                    subheading: "Important details about the report",
                    data: {
                      item1: "Value 1",
                      item2: "Value 2",
                      item3: "Value 3"
                    }
                  }
                }
              ];

            setSections(defaultSections);
            setActiveSectionId(defaultSections[0].id);
            setActiveSectionTitle(defaultSections[0].title);
          }
        }

        // Fetch clients
        try {
          const clientsResponse = await fetch('/assets/mock-data/db.json');
          if (clientsResponse.ok) {
            const data = await clientsResponse.json();
            
            // Extract clients from the db.json file
            if (data.clients && Array.isArray(data.clients)) {
              setClients(data.clients);
            } else {
              // Fallback to mock clients
              const fallbackClients = [
                { id: '1', name: 'TechCorp Solutions' },
                { id: '2', name: 'HealthPlus Medical' },
                { id: '3', name: 'Global Finance Group' },
                { id: '4', name: 'Green Energy Solutions' },
                { id: '5', name: 'ConstructPro Builders' }
              ];
              setClients(fallbackClients);
            }
          } else {
            // Fallback to mock clients
            const fallbackClients = [
              { id: '1', name: 'TechCorp Solutions' },
              { id: '2', name: 'HealthPlus Medical' },
              { id: '3', name: 'Global Finance Group' },
              { id: '4', name: 'Green Energy Solutions' },
              { id: '5', name: 'ConstructPro Builders' }
            ];
            setClients(fallbackClients);
          }
        } catch (error) {
          console.error('Error fetching clients:', error);

          // Fallback to mock clients
          const fallbackClients = [
            { id: '1', name: 'TechCorp Solutions' },
            { id: '2', name: 'HealthPlus Medical' },
            { id: '3', name: 'Global Finance Group' },
            { id: '4', name: 'Green Energy Solutions' },
            { id: '5', name: 'ConstructPro Builders' }
          ];
          setClients(fallbackClients);
        }

        // Fetch report data if editing an existing report
        if (reportId) {
          try {
            const reportResponse = await fetch('/assets/mock-data/db.json');
            if (reportResponse.ok) {
              const data = await reportResponse.json();
              
              // Find the report with the matching ID
              const reportData = data.reports.find((report: any) => report.reportId === reportId) || {};

              // Set report info
              setReportInfo({
                title: reportData.reportName || "Untitled Report",
                description: reportData.description || "",
                client: reportData.clientId ? {
                  'client-id': reportData.clientId,
                  'company-name': reportData.clientName || ""
                } : undefined,
                startDate: reportData.startDate || new Date().toISOString().split('T')[0],
                endDate: reportData.endDate || new Date().toISOString().split('T')[0]
              });

              // Set sections
              if (reportData.content?.sections) {
                const convertedSections = reportData.content.sections.map((section: any) => {
                  const sectionTitle = section.title || section['section-title'] || "Untitled Section";
                  return {
                    id: section.id || `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    title: sectionTitle,
                    type: section.type || "title-page",
                    content: section.content || {}
                  };
                });

                setSections(convertedSections);
                if (convertedSections.length > 0) {
                  setActiveSectionId(convertedSections[0].id);
                  setActiveSectionTitle(convertedSections[0].title);
                }
              }

              // Set template and styles
              if (reportData.content?.template) {
                setSelectedTemplate(reportData.content.template);
              }
              if (reportData.style) {
                setStyles(reportData.style);
              }
            }
          } catch (error) {
            console.error('Error fetching report:', error);
            setError('Failed to load report. Please try again later.');
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error in data initialization:', err);
        setError('Failed to initialize editor. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [reportId, templateId]);

  // Handle menu item click
  const handleMenuClick = (item: string) => {
    if (activeItem === item) {
      setActiveItem(null);
    } else {
      setActiveItem(item);
      setActiveSectionId(null); // Reset section panel when changing menu items
    }
  };

  // Handle section click
  const handleSectionClick = (id: string, title: string) => {
    setActiveSectionId(id);
    setActiveSectionTitle(title);
    // Trigger pulse animation
    setPulsingSectionId(id);
    // Reset pulse animation after it completes
    setTimeout(() => {
      setPulsingSectionId(null);
    }, 1000);
    // Keep the panel open, don't hide it
    // setActiveItem(null); // Hide the main panel
  };

  // Handle back to sections
  const handleBackToSections = () => {
    setActiveSectionId(null);
    setActiveItem('Sections'); // Show the sections panel again
  };

  // Handle return to reports
  const handleReturnToReports = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push('/client/reports');
    }
  };

  // Handle save report
  const handleSaveReport = () => {
    // Convert the data to the API format
    const convertedData = {
      reportName: reportInfo.title,
      description: reportInfo.description,
      clientId: reportInfo.client ? reportInfo.client['client-id'] : null,
      clientName: reportInfo.client ? reportInfo.client['company-name'] : null,
      status: "Draft",
      author: "Current User",
      startDate: reportInfo.startDate,
      endDate: reportInfo.endDate,
      content: {
        template: selectedTemplate,
        sections: sections.map(section => ({
          id: section.id,
          'section-title': section.title,
          type: section.type,
          content: section.content
        }))
      },
      style: styles
    };

    if (onSave) {
      onSave(convertedData);
      return;
    }

    // If it's a new report, create it
    if (!reportId) {
      // In a real app, this would save to the server
      // For now, just simulate success and redirect
      console.log('Would save report:', convertedData);
      setTimeout(() => {
        router.push('/client/reports');
      }, 500);
      
      /* Commented out server call since we're using local mock data
      fetch('/assets/mock-data/db.json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(convertedData)
      })
      */
    } else {
      // Update existing report
      // In a real app, this would update the server
      // For now, just simulate success and redirect
      console.log('Would update report:', reportId, convertedData);
      setTimeout(() => {
        router.push('/client/reports');
      }, 500);
      
      /* Commented out server call since we're using local mock data
      fetch(`/assets/mock-data/db.json`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(convertedData)
      })
      */
    }
  };

  // Handle update report info
  const handleUpdateReportInfo = (field: string, value: any) => {
    setReportInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle update section
  const handleUpdateSection = (updatedSection: Section) => {
    setSections(sections.map(section => 
      section.id === updatedSection.id ? updatedSection : section
    ));
  };

  // Handle add section
  const handleAddSection = () => {
    const newSection: Section = {
      id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: `New Section ${sections.length + 1}`,
      type: 'content-section',
      content: {
        content: 'Enter your content here...'
      }
    };

    setSections([...sections, newSection]);
  };

  // Handle template select
  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);

    // Find the selected template
    const selectedTemplate = templates.find(t => t.id === templateId);
    if (selectedTemplate) {
      setTemplateName(selectedTemplate.name);

      // Ask for confirmation before replacing sections
      if (sections.length > 0) {
        if (confirm("Changing the template will replace your current sections. Are you sure you want to continue?")) {
          // Replace sections with template sections
          if (selectedTemplate.sections && selectedTemplate.sections.length > 0) {
            const templateSections = selectedTemplate.sections.map((section: any) => {
              const sectionTitle = section.title || "Untitled Section";
              return {
                id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                title: sectionTitle,
                type: section.type || "title-page",
                content: section.content || {}
              };
            });

            setSections(templateSections);
            if (templateSections.length > 0) {
              setActiveSectionId(templateSections[0].id);
              setActiveSectionTitle(templateSections[0].title);
            }
          }
        }
      } else {
        // If no sections, just add the template sections
        if (selectedTemplate.sections && selectedTemplate.sections.length > 0) {
          const templateSections = selectedTemplate.sections.map((section: any) => {
            const sectionTitle = section.title || "Untitled Section";
            return {
              id: `section-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              title: sectionTitle,
              type: section.type || "title-page",
              content: section.content || {}
            };
          });

          setSections(templateSections);
          if (templateSections.length > 0) {
            setActiveSectionId(templateSections[0].id);
            setActiveSectionTitle(templateSections[0].title);
          }
        }
      }
    }
  };

  // Handle update style
  const handleUpdateStyle = (field: string, value: any) => {
    setStyles(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Get active section
  const getActiveSection = () => {
    if (!activeSectionId) return null;
    return sections.find(section => section.id === activeSectionId) || null;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <div className="p-4 bg-red-50 text-red-500 rounded-md mb-4">
          <p>{error}</p>
        </div>
        <button onClick={handleReturnToReports} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        activeItem={activeItem}
        onMenuClick={handleMenuClick}
        onSectionClick={handleSectionClick}
        onReturnToReports={handleReturnToReports}
        onSaveReport={handleSaveReport}
        reportTitle={reportInfo.title}
      />

      {/* Main Layout Area - Two-Column Layout */}
      <div className="flex-1 ml-64 flex">
        {/* Panel Content - Integrated as a column instead of absolute position */}
        {activeItem && (
          <div className="w-96 h-[calc(100vh-4rem)] flex-shrink-0 overflow-hidden border-r">
            <PanelContent
              title={activeItem}
              onClose={() => setActiveItem(null)}
              onSectionClick={handleSectionClick}
              reportInfo={reportInfo}
              clients={clients}
              onUpdateReportInfo={handleUpdateReportInfo}
              templateName={templateName}
              sections={sections}
              onAddSection={handleAddSection}
              onUpdateSection={handleUpdateSection}
              templates={templates}
              selectedTemplate={selectedTemplate}
              onTemplateSelect={handleTemplateSelect}
              styles={styles}
              onUpdateStyle={handleUpdateStyle}
            />
          </div>
        )}

        {/* Preview Area */}
        <div className="flex-1 overflow-auto">
          <PreviewArea
            section={getActiveSection()}
            onUpdateSection={handleUpdateSection}
            allSections={sections}
            styleTemplateId={styles.styleTemplateId}
            pulsingSectionId={pulsingSectionId}
          />
        </div>
      </div>
    </div>
  );
}
