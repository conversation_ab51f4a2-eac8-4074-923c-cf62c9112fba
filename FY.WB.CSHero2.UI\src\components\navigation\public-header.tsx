"use client";

import Link from "next/link";
import { Users, UserPlus, Menu, X, LogOut } from "lucide-react";
import { cn } from "@/lib/utils";
import { mainMenu } from "./menus/main-menu";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function PublicHeader() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const isActive = (href: string) => pathname === href;
  const isClientOrAdmin = pathname.startsWith('/client') || pathname.startsWith('/admin');
  const { user, logout } = useAuth();

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-nav-bg shadow-md">
        <div className="mx-auto max-w-[1920px]">
          <div className="flex h-16 items-center px-4">
            {/* Logo */}
            <Link href="/" className="flex shrink-0 items-center">
              <span className="text-xl font-bold text-white">
                CS-Hero
              </span>
            </Link>

            {/* Navigation Group (Menu + Buttons) */}
            <div className="ml-auto flex items-center gap-8">
              {/* Mobile Menu Button - Only on public pages */}
              {!isClientOrAdmin && (
                <button
                  type="button"
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className="md:hidden text-white hover:text-white/80"
                >
                  {mobileMenuOpen ? (
                    <X className="h-6 w-6" />
                  ) : (
                    <Menu className="h-6 w-6" />
                  )}
                </button>
              )}

              {/* Main Navigation - Desktop (hidden on client/admin pages) */}
              <nav className={cn(
                "hidden md:flex items-center",
                isClientOrAdmin && "!hidden"
              )}>
                {mainMenu.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "px-3 py-2 text-sm font-medium",
                      "text-white/80 hover:text-white transition-colors",
                      isActive(item.href) && "text-white"
                    )}
                  >
                    {item.label}
                  </Link>
                ))}
              </nav>

              {/* Auth Buttons or User Dropdown */}
              <div className="flex items-center gap-4">
                {user ? (
                  <UserDropdown user={user} onLogout={logout} />
                ) : (
                  <>
                    <Link
                      href="/register"
                      className={cn(
                        "flex items-center gap-2 rounded-lg bg-white/10 px-4 py-2",
                        "text-white hover:bg-white/20 transition-colors"
                      )}
                    >
                      <UserPlus className="h-5 w-5" />
                      <span className="hidden sm:inline">Register</span>
                    </Link>
                    <Link
                      href="/login"
                      className={cn(
                        "flex items-center gap-2 rounded-lg bg-white/10 px-4 py-2",
                        "text-white hover:bg-white/20 transition-colors"
                      )}
                    >
                      <Users className="h-5 w-5" />
                      <span>Login</span>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu - Only shown on public pages */}
      {!isClientOrAdmin && mobileMenuOpen && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden">
          <div className="fixed right-0 top-16 w-64 bg-[rgb(var(--primary))] h-screen shadow-lg">
            <nav className="flex flex-col p-4">
              {mainMenu.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "px-3 py-2 text-sm font-medium",
                    "text-white/80 hover:text-white transition-colors",
                    isActive(item.href) && "text-white"
                  )}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
            </nav>
          </div>
        </div>
      )}
    </>
  );
}

// User dropdown component for authenticated users
interface UserDropdownProps {
  user: {
    email: string;
    userId: string;
  };
  onLogout: () => Promise<void>;
}

function UserDropdown({ user, onLogout }: UserDropdownProps) {
  // Get user initials for avatar
  const getInitials = () => {
    if (!user?.email) return "U";
    return user.email
      .split("@")[0]
      .slice(0, 2)
      .toUpperCase();
  };

  const handleLogout = async () => {
    try {
      await onLogout();
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          type="button"
          className="h-10 w-10 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center text-sm font-medium text-white"
        >
          {getInitials()}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="px-2 py-1.5 text-sm">
          <p className="font-medium text-foreground">{user.email}</p>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer text-red-600 focus:text-red-600"
          onClick={handleLogout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
