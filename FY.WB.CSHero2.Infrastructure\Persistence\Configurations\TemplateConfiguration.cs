using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class TemplateConfiguration : IEntityTypeConfiguration<Template>
    {
        public void Configure(EntityTypeBuilder<Template> builder)
        {
            builder.HasKey(t => t.Id);

            builder.Property(t => t.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(t => t.Description)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(t => t.Category)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(t => t.ThumbnailUrl)
                .IsRequired()
                .HasMaxLength(255);

            // JSON storage for tags
            builder.Property(t => t.Tags)
                .IsRequired()
                .HasColumnType("nvarchar(max)"); // Store as JSON string

            // JSON storage for sections
            builder.Property(t => t.Sections)
                .IsRequired()
                .HasColumnType("nvarchar(max)"); // Store as JSON string

            // JSON storage for fields
            builder.Property(t => t.Fields)
                .IsRequired()
                .HasColumnType("nvarchar(max)"); // Store as JSON string

            // Create an index on Category for faster lookups
            builder.HasIndex(t => t.Category);

            // Create a composite index on Name and Category
            builder.HasIndex(t => new { t.Name, t.Category });

            // Audit properties are handled by base entity configuration
            builder.Property(t => t.CreationTime)
                .IsRequired();

            builder.Property(t => t.LastModificationTime)
                .IsRequired(false);

            // Configure default values for JSON fields
            builder.Property(t => t.Tags)
                .HasDefaultValue("[]");

            builder.Property(t => t.Sections)
                .HasDefaultValue("[]");

            builder.Property(t => t.Fields)
                .HasDefaultValue("[]");
        }
    }
}
