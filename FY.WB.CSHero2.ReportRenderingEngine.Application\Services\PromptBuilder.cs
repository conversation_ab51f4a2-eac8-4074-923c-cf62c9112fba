using System;
using System.Collections.Generic;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Common.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Static helper for building prompt objects from template data and metadata
    /// </summary>
    public static class PromptBuilder
    {
        /// <summary>
        /// Builds a structured RenderRequest object that will be serialized to XML
        /// </summary>
        /// <param name="metadata">Template metadata defining structure</param>
        /// <param name="data">Content data to populate the template</param>
        /// <param name="existingHtml">Optional existing HTML to be preserved</param>
        /// <param name="instructionPrompt">Instructions for the LLM</param>
        /// <returns>A fully populated RenderRequest object</returns>
        public static RenderRequest BuildPromptObject(
            DocumentTemplateMetadata metadata, 
            Dictionary<string, object> data, 
            string? existingHtml, 
            string instructionPrompt)
        {
            if (metadata == null) throw new ArgumentNullException(nameof(metadata));
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (instructionPrompt == null) throw new ArgumentNullException(nameof(instructionPrompt));

            var request = new RenderRequest
            {
                Style = new ReportStyle
                {
                    Theme = metadata.Style?.Theme ?? string.Empty,
                    Font = metadata.Style?.Font ?? string.Empty,
                    PrimaryColor = metadata.Style?.PrimaryColor ?? string.Empty,
                    AccentColor = metadata.Style?.AccentColor ?? string.Empty
                },
                Sections = new List<RenderSection>(),
                ExistingHtml = new CDataWrapper(existingHtml ?? string.Empty),
                UserInstruction = instructionPrompt
            };

            // Map metadata sections and fields to render sections and fields
            foreach (var section in metadata.Sections ?? new List<ReportSection>())
            {
                var renderSection = new RenderSection
                {
                    Id = section.Id ?? $"section-{Guid.NewGuid()}",
                    Name = section.Name ?? string.Empty,
                    Description = section.Description ?? string.Empty,
                    Fields = new List<RenderField>()
                };

                // Map fields and their values from the data dictionary
                foreach (var field in section.Fields ?? new List<ReportField>())
                {
                    // Safely extract field value from data dictionary
                    data.TryGetValue(field.Id, out var value);
                    
                    renderSection.Fields.Add(new RenderField
                    {
                        Id = field.Id ?? $"field-{Guid.NewGuid()}",
                        Name = field.Name ?? string.Empty,
                        Type = field.Type ?? "text",
                        Description = field.Description ?? string.Empty,
                        Value = FormatFieldValue(value, field.Type)
                    });
                }

                request.Sections.Add(renderSection);
            }

            return request;
        }

        /// <summary>
        /// Formats field values based on their type
        /// </summary>
        private static string FormatFieldValue(object value, string fieldType)
        {
            if (value == null) return string.Empty;

            // Custom formatting based on field type
            return fieldType?.ToLowerInvariant() switch
            {
                "date" => value is DateTime date ? date.ToString("yyyy-MM-dd") : value.ToString(),
                "number" => value is IFormattable formattable ? formattable.ToString("0.##", null) : value.ToString(),
                "boolean" => value is bool boolValue ? boolValue.ToString().ToLowerInvariant() : value.ToString(),
                _ => value.ToString() ?? string.Empty
            };
        }
    }
}
