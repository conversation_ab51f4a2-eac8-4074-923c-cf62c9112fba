using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class ReportConfiguration : IEntityTypeConfiguration<Report>
    {
        public void Configure(EntityTypeBuilder<Report> builder)
        {
            builder.HasKey(r => r.Id);

            builder.Property(r => r.ReportNumber)
                .IsRequired()
                .HasMaxLength(20); // e.g., "CSR-2025-001"

            builder.HasIndex(r => r.ReportNumber)
                .IsUnique();

            builder.Property(r => r.ClientId)
                .IsRequired();

            builder.Property(r => r.ClientName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(r => r.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(r => r.Category)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.SlideCount)
                .IsRequired();

            builder.Property(r => r.Status)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.Author)
                .IsRequired()
                .HasMaxLength(100);

            // Configure relationship with Client
            builder.HasOne(r => r.Client)
                .WithMany() // No navigation property on Client side yet
                .HasForeignKey(r => r.ClientId)
                .OnDelete(DeleteBehavior.Restrict); // Don't cascade delete reports when client is deleted

            // Audit properties are handled by base entity configuration
            builder.Property(r => r.CreationTime)
                .IsRequired();

            builder.Property(r => r.LastModificationTime)
                .IsRequired(false);
        }
    }
}
