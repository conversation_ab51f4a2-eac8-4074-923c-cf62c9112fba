using Microsoft.Extensions.DependencyInjection;
using FY.WB.CSHero2.Application.Clients.Queries;
using MediatR;
using FluentValidation;
using FluentValidation.AspNetCore;

namespace FY.WB.CSHero2.StartupConfiguration
{
    public static class MediatRConfiguration
    {
        public static IServiceCollection AddMediatRConfiguration(this IServiceCollection services)
        {
            // Add MediatR
            services.AddMediatR(cfg => 
                cfg.RegisterServicesFromAssembly(typeof(GetClientsQuery).Assembly));
            
            // Add FluentValidation
            services.AddValidatorsFromAssemblyContaining<GetClientsQuery>();
            services.AddFluentValidationAutoValidation();

            return services;
        }
    }
}
