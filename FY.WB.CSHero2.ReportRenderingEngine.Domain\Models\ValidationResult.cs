using System.Collections.Generic;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Models
{
    /// <summary>
    /// Result of HTML validation
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Indicates whether the validation was successful
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Collection of validation errors, if any
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();
    }
}
