import { NextRequest, NextResponse } from 'next/server';
import { getTemplates, createTemplate } from '@/lib/data/templates';

/**
 * GET /api/templates
 * Returns a list of templates with optional filtering, sorting, and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const limit = searchParams.get('_limit');
    const page = searchParams.get('_page');
    const sortBy = searchParams.get('_sort') || undefined;
    const order = searchParams.get('_order') as 'asc' | 'desc' | undefined;
    
    // Build query object from all other parameters
    const query: Record<string, string | string[]> = {};
    searchParams.forEach((value, key) => {
      if (!['_limit', '_page', '_sort', '_order'].includes(key)) {
        query[key] = value;
      }
    });
    
    const templates = await getTemplates({
      query,
      limit: limit ? parseInt(limit) : undefined,
      page: page ? parseInt(page) : undefined,
      sortBy,
      order
    });
    
    return NextResponse.json(templates);
  } catch (error) {
    console.error('Error fetching templates:', error);
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/templates
 * Creates a new template
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const newTemplate = await createTemplate(body);
    
    return NextResponse.json(newTemplate, { status: 201 });
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
}
