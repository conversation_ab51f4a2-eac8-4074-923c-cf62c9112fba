# Active Context: SaaS Template Development

**Version:** 0.1.9
**Date:** 2025-05-23
**Related Documents:** `projectbrief.md`, `productContext.md`, `systemPatterns.md`, `techContext.md`, `progress.md`

## 1. Current Work Focus

**Phase 3.5: Implement Backend CQRS and Controllers for Core Entities - GET Operations is COMPLETE for regular user scenarios.**
- All core entities (`Form`, `Invoice`, `Report`, `Template`, `TenantProfile`, `Upload`) now have backend CQRS GET operations implemented.
- Controllers have been updated to use MediatR.
- DTOs and Query Handlers are in place.
- Multi-tenancy for regular users has been verified through backend API testing (Swagger UI) and initial frontend dashboard loading.

**Deferred Items from Phase 3.5 (to be addressed in a future phase):**
- Comprehensive testing of Admin user paths for all new GET endpoints.
- Resolution of Admin login issues.
- Fixing minor frontend React key warning in `ReportTable`.
- Updating the reports BFF API route to correctly map frontend sort/pagination parameters to backend CQRS query parameters.
- Writing a full suite of automated integration tests for all new controller endpoints.

The secondary stream, **Report Rendering Engine Implementation**, remains a parallel effort with its own set of pending tasks.

## 2. Recent Changes & Accomplishments

-   **Phase 3.5 Completion (GET Operations for Regular Users):**
    -   Successfully implemented CQRS GET operations (Controllers, DTOs, Queries, Handlers) for:
        -   `FormsController`
        -   `InvoicesController`
        -   `ReportsController`
        -   `TemplatesController`
        -   `TenantProfilesController` (including specific logic for admin vs. regular user in handlers)
        -   `UploadsController`
    -   Verified backend functionality for these GET endpoints via Swagger UI for regular user scenarios.
    -   Confirmed frontend dashboard loads data (Clients, Reports) successfully for regular users, indicating successful integration for these paths.
    -   Updated `memory-bank/progress.md` to reflect this completion.

-   **Previous Accomplishments (Forms Controller, Test Framework, Report Rendering Engine initial phases, Phase 3 Frontend API Reconfig, Auth Enhancements, Data Migration Phases 1 & 2, Startup Hang Resolution, etc.) remain valid and foundational to this progress.**

*Previous accomplishments (Memory Bank Init, API/Frontend Phase 1 & 2 Recaps, Full-Stack Auth Flow) remain valid.*

## 3. Current State of Actively Running Processes

-   **Next.js Dev Server (`un_wb_templateproject_ui`):** Running on `http://localhost:3000`.
-   **ASP.NET Core API (`UN_WB_TemlateProject`):** Running on `http://localhost:5056`.

## 4. Key Decisions & Considerations

-   **Deferred Admin Path Testing:** Full testing and potential fixes for admin-specific data access paths for the new GET endpoints are deferred to a future phase to maintain momentum on core user-facing features.
-   **Deferred Frontend Fixes:** Minor frontend issues (React key warning, report API parameter mapping) are deferred.
-   **Testing Strategy for Multi-Tenant Controllers:**
    -   Implemented mocking approach for controller tests to isolate business logic (for `FormsController`).
    -   Created test fixtures to verify multi-tenant data isolation.
    -   Simulated tenant contexts to ensure controllers respect tenant boundaries.
    -   Used both in-memory database and mock objects for different testing scenarios.
    -   Manual testing via Swagger UI has been the primary method for verifying new GET endpoints for regular users.

-   **Report Rendering Engine Architecture:** (No change)
    -   Implemented a clean separation of concerns following Clean Architecture principles.
    -   Designed to support multiple LLM providers (OpenAI, Anthropic) through a factory pattern.
    -   Included performance optimizations like parallel data loading and prompt caching.
    -   Used XML serialization for structured prompting of LLMs.
    -   Structured the code to follow the same patterns as the rest of the solution.

-   **BFF Pattern for API Calls:** (No change) We've decided to maintain the Backend-For-Frontend (BFF) pattern.

-   **Error Handling and Logging:** (No change) We've added comprehensive error handling and logging.

-   **Authentication Configuration:** (No change) We've updated the authentication configuration.

-   **Data Seeding Order:** (No change) We've established a proper ordering for entity seeding.

-   **Finbuckle CS0308 Blocker:** (No change) Resolved by implementing proper auditing abstractions and moving `AppTenantInfo` to the Infrastructure layer.

-   **Next.js `/client/dashboard` 404 Error:** (No change - but dashboard is now loading data) This route returns a 404 despite the file structure (`src/app/(client)/dashboard/page.tsx`) appearing correct and the dev server (including `.next` folder) being restarted. This is a known issue requiring further local investigation. *Update: User reports dashboard loaded successfully for Alex, so this might be an intermittent or context-specific issue.*

-   **Hydration Errors:** (No change) Likely due to browser extensions; `suppressHydrationWarning` is in place.

## 5. Next Immediate Steps

With Phase 3.5 (GET operations) largely complete for user-facing scenarios, potential next steps include:

1.  **Address Deferred Items from Phase 3.5:**
    *   Fix Admin login and test Admin paths for all GET endpoints.
    *   Resolve the React key warning in `ReportTable`.
    *   Update the reports BFF API route for correct parameter mapping.
    *   Write comprehensive automated integration tests.

2.  **Begin Phase 4: Backend CQRS Implementation for CUD Operations:**
    *   Implement Create, Update, Delete (CUD) commands, handlers, and controller actions for the entities covered in Phase 3.5 (`Form`, `Invoice`, `Report`, `Template`, `TenantProfile`, `Upload`).

3.  **Continue with Phase 5: Full Stack Verification & Documentation:**
    *   Thoroughly test frontend pages that will consume data from the new backend API endpoints (beyond initial dashboard load).
    *   Update remaining Memory Bank documentation (`systemPatterns.md`, `frontend_backend_integration_plan.md`).

4.  **Continue Report Rendering Engine Implementation (Project 2):**
    *   Focus on Phase 4: Integration and Configuration (Database, LLM API, HTML Validation).

## 6. Important Patterns & Preferences to Remember

```
FY.WB.CSHero2/
├── FY.WB.CSHero2.Domain/                 # Domain layer (entities, interfaces)
│   ├── Entities/
│   │   ├── Client.cs                     # Client domain entity
│   │   ├── Report.cs                     # Report domain entity
│   │   ├── Form.cs                       # Form domain entity
│   │   ├── Upload.cs                     # Upload domain entity
│   │   ├── Template.cs                   # Template domain entity
│   │   ├── TenantProfile.cs              # TenantProfile domain entity
│   │   ├── Invoice.cs                    # Invoice domain entity
│   │   └── Core/                         # Base entity classes
│   │       ├── Entity.cs                 # Base entity with ID
│   │       ├── AuditedEntity.cs          # Entity with audit properties
│   │       ├── FullAuditedEntity.cs      # Entity with full audit properties
│   │       ├── MultiTenantEntity.cs      # Entity with tenant ID
│   │       └── FullAuditedMultiTenantEntity.cs # Combined auditing and multi-tenancy
│   └── Interfaces/                       # Domain interfaces
│
├── FY.WB.CSHero2.Application/            # Application layer (CQRS, services)
│   ├── Clients/                          # Client-related application logic
│   │   ├── Commands/                     # Client commands (Create, Update, Delete, Archive)
│   │   ├── Dtos/                         # Client DTOs
│   │   └── Queries/                      # Client queries (GetById, GetAll)
│   ├── Forms/                            # Form-related application logic
│   │   ├── Dtos/                         # Form DTOs
│   │   └── Queries/                      # Form queries (GetById, GetAll)
│   └── Common/                           # Shared application components
│       └── Interfaces/                   # Application interfaces
│
├── FY.WB.CSHero2.Infrastructure/         # Infrastructure layer (EF Core, Identity)
│   └── Persistence/                      # Database-related code
│       ├── ApplicationDbContext.cs       # EF Core DbContext
│       ├── AppTenantInfo.cs              # Tenant information class
│       ├── ApplicationUser.cs            # Custom Identity user
│       ├── Configurations/               # EF Core entity configurations
│       │   ├── ClientConfiguration.cs    # Client entity configuration
│       │   ├── ReportConfiguration.cs    # Report entity configuration
│       │   ├── FormConfiguration.cs      # Form entity configuration
│       │   ├── UploadConfiguration.cs    # Upload entity configuration
│       │   ├── TemplateConfiguration.cs  # Template entity configuration
│       │   ├── TenantProfileConfiguration.cs # TenantProfile entity configuration
│       │   └── InvoiceConfiguration.cs   # Invoice entity configuration
│       ├── Migrations/                   # EF Core migrations
│       └── Seeders/                      # Database seed data
│           └── DataSeeder.cs             # Data seeding logic
│
├── FY.WB.CSHero2/                        # API layer (controllers, startup)
│   ├── Controllers/                      # API controllers
│   │   ├── AuthController.cs             # Authentication endpoints
│   │   ├── ClientsController.cs          # Client endpoints
│   │   ├── ReportsController.cs          # Report endpoints
│   │   ├── FormsController.cs            # Form endpoints
│   │   ├── UploadsController.cs          # Upload endpoints
│   │   ├── TemplatesController.cs        # Template endpoints
│   │   ├── TenantProfilesController.cs   # TenantProfile endpoints
│   │   └── WeatherForecastController.cs  # Example controller
│   └── Program.cs                        # Application startup and configuration
│
├── FY.WB.CSHero2.Test/                   # Test project
│   ├── TestBase.cs                       # Base class for tests
│   └── ControllerTests/                  # Controller tests
│       └── FormsControllerTests.cs       # Tests for FormsController
│
└── FY.WB.CSHero2.UI/                     # Next.js frontend
    ├── src/
    │   ├── app/                          # Next.js App Router
    │   │   ├── (public)/                 # Public routes
    │   │   ├── (client)/                 # Client routes
    │   │   ├── admin/                    # Admin routes
    │   │   ├── api/                      # BFF API routes
    │   │   │   └── v1/                   # API version 1
    │   │   │       ├── clients/          # Client-related API routes
    │   │   │       └── reports/          # Report-related API routes
    │   │   └── layout.tsx                # Root layout
    │   ├── components/                   # React components
    │   ├── lib/                          # Utility functions
    │   │   ├── api.ts                    # API client functions
    │   │   └── constants.ts              # Constants including API URLs
    │   └── types/                        # TypeScript type definitions
    └── public/                           # Static assets
```

### Report Rendering Engine Structure

```
FY.WB.CSHero2.ReportRenderingEngine.Domain/
├── Interfaces/
│   ├── IDatabaseService.cs
│   ├── ILlmClient.cs
│   └── IHtmlValidator.cs
├── Models/
│   ├── LlmMetrics.cs
│   └── ValidationResult.cs
└── Entities/
    ├── DocumentTemplateMetadata.cs
    ├── ReportStyle.cs
    ├── ReportSection.cs
    └── ReportField.cs

FY.WB.CSHero2.ReportRenderingEngine.Application/
├── Common/
│   └── Models/
│       ├── CDataWrapper.cs
│       ├── RenderRequest.cs
│       ├── ReportStyle.cs
│       ├── RenderSection.cs
│       └── RenderField.cs
└── Services/
    ├── PromptBuilder.cs
    ├── XmlPromptSerializer.cs
    └── ReportRenderer.cs

FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/
├── Models/
│   └── LlmConfig.cs
└── Services/
    ├── LlmClientFactory.cs
    ├── OpenAiLlmClient.cs
    └── AnthropicLlmClient.cs
```

## 7. Project File Structure

-   Build and fix bugs systematically.
-   Follow Clean Architecture principles for backend development.
-   Use CQRS with MediatR for application logic.
-   Implement seed data for development and testing.
-   The deployment target is Azure Static Web Apps + AppService + Database.
