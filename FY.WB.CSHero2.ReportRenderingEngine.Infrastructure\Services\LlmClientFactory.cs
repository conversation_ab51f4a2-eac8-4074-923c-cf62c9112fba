using System;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services
{
    /// <summary>
    /// Factory for creating different types of LLM clients
    /// </summary>
    public class LlmClientFactory
    {
        private readonly ILogger _logger;
        
        /// <summary>
        /// Constructor with logger dependency
        /// </summary>
        /// <param name="logger">The logger</param>
        public LlmClientFactory(ILogger<LlmClientFactory> logger)
        {
            _logger = logger;
        }
        
        /// <summary>
        /// Creates an appropriate LLM client based on configuration
        /// </summary>
        /// <param name="config">The LLM configuration</param>
        /// <returns>An instance of ILlmClient</returns>
        /// <exception cref="ArgumentException">Thrown when the provider is not supported</exception>
        public ILlmClient CreateClient(LlmConfig config)
        {
            return config.Provider?.ToLowerInvariant() switch
            {
                "anthropic" => new AnthropicLlmClient(config, _logger),
                "openai" => new OpenAiLlmClient(config, _logger),
                _ => throw new ArgumentException($"Unsupported LLM provider: {config.Provider}")
            };
        }
    }
}
