using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Text;
using System.Threading.Tasks;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Infrastructure.Services;
using Finbuckle.MultiTenant;
using Finbuckle.MultiTenant.Abstractions;
using FY.WB.CSHero2.StartupConfiguration;
using FY.WB.CSHero2.Infrastructure.Persistence.Seeders;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;

// Get logger factory
var loggerFactory = LoggerFactory.Create(builder =>
{
    builder
        .AddConsole()
        .AddDebug()
        .SetMinimumLevel(LogLevel.Information);
});
var logger = loggerFactory.CreateLogger<Program>();

// Log the connection string
var connectionString = configuration.GetConnectionString("DefaultConnection");
logger.LogInformation("Connection String: {ConnectionString}", connectionString);

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add database configuration
builder.Services.AddDatabaseConfiguration(configuration);

// Add identity configuration
builder.Services.AddIdentityConfiguration();

// Add authentication configuration
builder.Services.AddAuthenticationConfiguration(configuration);

// Add multi-tenancy configuration
builder.Services.AddMultiTenancyConfiguration();

// Add MediatR configuration
builder.Services.AddMediatRConfiguration();

// Add HttpContext and current user services
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

// Add other custom services (like CompanyProfileService)
builder.Services.AddServiceConfiguration();

// Add controllers and Swagger
builder.Services.AddControllers();

// Add Swagger/OpenAPI support
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerConfiguration();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    // Add Swagger middleware before other middleware
    app.UseSwaggerConfiguration();
}

// Use CORS middleware
app.UseCors("AllowAll");

// Add routing middleware
app.UseRouting();

// Add multi-tenancy middleware
app.UseMultiTenancyConfiguration();

// Add authentication and authorization middleware
app.UseAuthentication();
app.UseAuthorization();

// Database migration and seeding
logger.LogInformation("Initializing database...");
try
{
    using (var scope = app.Services.CreateScope())
    {
        logger.LogInformation("Created service scope");
        var services = scope.ServiceProvider;

        try
        {
            logger.LogInformation("Getting ApplicationDbContext...");
            var context = services.GetRequiredService<ApplicationDbContext>();
            logger.LogInformation("ApplicationDbContext obtained successfully");

            logger.LogInformation("Checking if database is SQL Server...");
            if (context.Database.IsSqlServer())
            {
                logger.LogInformation("Database is SQL Server, attempting to connect...");
                var canConnect = context.Database.CanConnect();
                logger.LogInformation("Can connect to database: {CanConnect}", canConnect);

                if (canConnect)
                {
                    logger.LogInformation("Attempting to migrate database...");
                    context.Database.Migrate();
                    logger.LogInformation("Database migration completed successfully");

                    // Seed data
                    logger.LogInformation("Starting data seeding...");
                    await FY.WB.CSHero2.Infrastructure.Persistence.Seeders.DataSeeder.SeedAsync(services);
                    logger.LogInformation("Data seeding completed successfully");
                }
                else
                {
                    logger.LogError("Cannot connect to database. Please check your connection string and SQL Server instance.");
                }
            }
            else
            {
                logger.LogWarning("Database is not SQL Server");
            }
        }
        catch (Exception innerEx)
        {
            logger.LogError(innerEx, "An error occurred during database initialization");
            if (innerEx.InnerException != null)
            {
                logger.LogError(innerEx.InnerException, "Inner exception during database initialization");
            }
        }
    }
}
catch (Exception ex)
{
    logger.LogError(ex, "An error occurred during database initialization");
    if (ex.InnerException != null)
    {
        logger.LogError(ex.InnerException, "Inner exception during database initialization");
    }
    logger.LogWarning("Application will proceed but database functionality may be limited");
}

// Routes
app.MapControllers();
app.MapGet("/api/ping", () =>
{
    logger.LogInformation("Ping endpoint called");
    return Results.Ok(new { message = "pong", timestamp = DateTime.UtcNow });
});

logger.LogInformation("API server starting up...");
app.Run();
