using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FY.WB.CSHero2.Application.Uploads.Dtos;
using NUnit.Framework;

namespace FY.WB.CSHero2.Test.ControllerTests
{
    [TestFixture]
    public class UploadsControllerTests : TestBase
    {
        private string _adminToken;
        private string _tenant1Token;
        private HttpClient _adminClient;
        private HttpClient _tenant1Client;

        [SetUp]
        public async Task Setup()
        {
            // Use the helper methods from TestBase
            _adminToken = await GetAdminToken();
            _tenant1Token = await GetTenant1Token();

            // Create authenticated clients
            _adminClient = CreateAuthenticatedClient(_adminToken);
            _tenant1Client = CreateAuthenticatedClient(_tenant1Token);
        }

        #region Create Tests

        [Test]
        public async Task CreateUpload_ValidData_ReturnsCreated()
        {
            // Arrange
            var createDto = new CreateUploadRequestDto
            {
                Filename = "test-document.pdf",
                Size = 1024000, // 1MB
                ContentType = "application/pdf",
                StoragePath = "/uploads/test-document.pdf",
                StorageProvider = "LocalStorage",
                ExternalUrl = "https://example.com/files/test-document.pdf",
                Checksum = "abc123def456"
            };

            var content = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PostAsync("/api/uploads", content);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Created), 
                $"Expected 201 Created but got {response.StatusCode}. Response: {await response.Content.ReadAsStringAsync()}");

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<JsonElement>(responseContent, _jsonOptions);
            
            Assert.That(result.TryGetProperty("id", out var idProperty), Is.True, "Response should contain an 'id' property");
            var uploadId = Guid.Parse(idProperty.GetString());
            
            Console.WriteLine($"Successfully created upload with ID: {uploadId}");

            // Verify the upload was created by retrieving it
            var getResponse = await _tenant1Client.GetAsync($"/api/uploads/{uploadId}");
            Assert.That(getResponse.IsSuccessStatusCode, Is.True, "Should be able to retrieve the created upload");
        }

        [Test]
        public async Task CreateUpload_InvalidData_ReturnsBadRequest()
        {
            // Arrange - Missing ALL required fields to ensure validation fails
            var createDto = new CreateUploadRequestDto
            {
                // All required fields are missing or invalid
                Filename = "", // Empty string should fail validation
                Size = 0, // Zero size should fail validation
                ContentType = "", // Empty string should fail validation
                StoragePath = "" // Empty string should fail validation
            };

            var content = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PostAsync("/api/uploads", content);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.BadRequest), 
                $"Expected 400 Bad Request but got {response.StatusCode}");

            var responseContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Validation error response: {responseContent}");
        }

        #endregion

        #region Update Tests

        [Test]
        public async Task UpdateUpload_ValidData_ReturnsNoContent()
        {
            // Arrange - First create an upload
            var createDto = new CreateUploadRequestDto
            {
                Filename = "original-file.txt",
                Size = 512,
                ContentType = "text/plain",
                StoragePath = "/uploads/original-file.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _tenant1Client.PostAsync("/api/uploads", createContent);
            Assert.That(createResponse.StatusCode, Is.EqualTo(HttpStatusCode.Created));

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Arrange - Update data
            var updateDto = new UpdateUploadRequestDto
            {
                Id = uploadId,
                Filename = "updated-file.txt",
                ContentType = "text/plain",
                StoragePath = "/uploads/updated-file.txt",
                Checksum = "updated-checksum"
            };

            var updateContent = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{uploadId}", updateContent);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.NoContent), 
                $"Expected 204 No Content but got {response.StatusCode}. Response: {await response.Content.ReadAsStringAsync()}");

            // Verify the upload was updated by retrieving it
            var getResponse = await _tenant1Client.GetAsync($"/api/uploads/{uploadId}");
            Assert.That(getResponse.IsSuccessStatusCode, Is.True);

            var getContent = await getResponse.Content.ReadAsStringAsync();
            var updatedUpload = JsonSerializer.Deserialize<UploadDto>(getContent, _jsonOptions);
            
            Assert.That(updatedUpload.Filename, Is.EqualTo("updated-file.txt"));
            Assert.That(updatedUpload.Checksum, Is.EqualTo("updated-checksum"));
            
            Console.WriteLine($"Successfully updated upload: {updatedUpload.Filename}");
        }

        [Test]
        public async Task UpdateUpload_NonExistentId_ReturnsNotFound()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();
            var updateDto = new UpdateUploadRequestDto
            {
                Id = nonExistentId,
                Filename = "non-existent.txt"
            };

            var content = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{nonExistentId}", content);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.NotFound), 
                $"Expected 404 Not Found but got {response.StatusCode}");
        }

        [Test]
        public async Task UpdateUpload_MismatchedId_ReturnsBadRequest()
        {
            // Arrange
            var routeId = Guid.NewGuid();
            var bodyId = Guid.NewGuid();
            
            var updateDto = new UpdateUploadRequestDto
            {
                Id = bodyId, // Different from route ID
                Filename = "test.txt"
            };

            var content = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{routeId}", content);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.BadRequest), 
                $"Expected 400 Bad Request but got {response.StatusCode}");
        }

        #endregion

        #region Delete Tests

        [Test]
        public async Task DeleteUpload_ExistingId_ReturnsNoContent()
        {
            // Arrange - First create an upload
            var createDto = new CreateUploadRequestDto
            {
                Filename = "to-be-deleted.txt",
                Size = 256,
                ContentType = "text/plain",
                StoragePath = "/uploads/to-be-deleted.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _tenant1Client.PostAsync("/api/uploads", createContent);
            Assert.That(createResponse.StatusCode, Is.EqualTo(HttpStatusCode.Created));

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Act
            var response = await _tenant1Client.DeleteAsync($"/api/uploads/{uploadId}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.NoContent), 
                $"Expected 204 No Content but got {response.StatusCode}. Response: {await response.Content.ReadAsStringAsync()}");

            // Verify the upload was deleted by trying to retrieve it
            var getResponse = await _tenant1Client.GetAsync($"/api/uploads/{uploadId}");
            Assert.That(getResponse.StatusCode, Is.EqualTo(HttpStatusCode.NotFound), 
                "Upload should not be found after deletion");
            
            Console.WriteLine($"Successfully deleted upload with ID: {uploadId}");
        }

        [Test]
        public async Task DeleteUpload_NonExistentId_ReturnsNotFound()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            // Act
            var response = await _tenant1Client.DeleteAsync($"/api/uploads/{nonExistentId}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.NotFound), 
                $"Expected 404 Not Found but got {response.StatusCode}");
        }

        #endregion

        #region Multi-Tenancy Tests

        [Test]
        public async Task UpdateUpload_CrossTenant_ReturnsForbidden()
        {
            // Arrange - Create an upload as admin (different tenant)
            var createDto = new CreateUploadRequestDto
            {
                Filename = "admin-file.txt",
                Size = 1024,
                ContentType = "text/plain",
                StoragePath = "/uploads/admin-file.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _adminClient.PostAsync("/api/uploads", createContent);
            Assert.That(createResponse.StatusCode, Is.EqualTo(HttpStatusCode.Created));

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Arrange - Try to update as tenant1
            var updateDto = new UpdateUploadRequestDto
            {
                Id = uploadId,
                Filename = "hacked-file.txt"
            };

            var updateContent = new StringContent(
                JsonSerializer.Serialize(updateDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            // Act
            var response = await _tenant1Client.PutAsync($"/api/uploads/{uploadId}", updateContent);

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Forbidden), 
                $"Expected 403 Forbidden but got {response.StatusCode}. Cross-tenant access should be denied.");
        }

        [Test]
        public async Task DeleteUpload_CrossTenant_ReturnsForbidden()
        {
            // Arrange - Create an upload as admin (different tenant)
            var createDto = new CreateUploadRequestDto
            {
                Filename = "admin-protected.txt",
                Size = 2048,
                ContentType = "text/plain",
                StoragePath = "/uploads/admin-protected.txt"
            };

            var createContent = new StringContent(
                JsonSerializer.Serialize(createDto, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var createResponse = await _adminClient.PostAsync("/api/uploads", createContent);
            Assert.That(createResponse.StatusCode, Is.EqualTo(HttpStatusCode.Created));

            var createResponseContent = await createResponse.Content.ReadAsStringAsync();
            var createResult = JsonSerializer.Deserialize<JsonElement>(createResponseContent, _jsonOptions);
            var uploadId = Guid.Parse(createResult.GetProperty("id").GetString());

            // Act - Try to delete as tenant1
            var response = await _tenant1Client.DeleteAsync($"/api/uploads/{uploadId}");

            // Assert
            Assert.That(response.StatusCode, Is.EqualTo(HttpStatusCode.Forbidden), 
                $"Expected 403 Forbidden but got {response.StatusCode}. Cross-tenant deletion should be denied.");
        }

        #endregion

        [TearDown]
        public void Cleanup()
        {
            _adminClient?.Dispose();
            _tenant1Client?.Dispose();
        }
    }
}
