"use client";

import { useState } from "react";
import { FileText, ChevronRight, ChevronLeft, Save, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Cal<PERSON>tor, Layout, Download } from "lucide-react";
import { Stepper, <PERSON>, StepLabel } from "@/components/ui/stepper";
import { DataTypeBuilder } from "@/components/features/reports/builder/data-type-builder";
import { TemplateBuilder } from "@/components/features/reports/builder/template-builder";
import { LayoutSuggestion } from "@/components/features/reports/builder/layout-suggestion";
import { LayoutRenderer } from "@/components/features/reports/builder/layout-renderer";
import { ExportOptions } from "@/components/features/reports/builder/export-options";
import { Checkbox } from "@/components/ui/checkbox";

const steps = [
  { id: "basics", title: "Basic Information" },
  { id: "data", title: "Data Input" },
  { id: "analysis", title: "Analysis Settings" },
  { id: "template", title: "Template Selection" },
  { id: "review", title: "Review & Generate" }
];

export default function CreateReportPage() {
  // Step navigation state
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [templateTab, setTemplateTab] = useState<'suggestion' | 'custom'>('suggestion');
  
  // Import the interfaces
  interface Field {
    id: string;
    name: string;
    label: string;
    type: string;
    required: boolean;
    options?: any;
  }

  interface DataType {
    id?: string;
    name: string;
    description?: string;
    fields: Field[];
  }

  interface TemplatePage {
    id: string;
    name: string;
    description?: string;
    dataElements: any[];
  }

  interface ReportTemplate {
    id?: string;
    name: string;
    description?: string;
    pages: TemplatePage[];
    dataTypeId?: string;
  }

  // Report data state
  const [reportData, setReportData] = useState({
    basics: {
      title: "",
      description: "",
      startDate: "",
      endDate: ""
    },
    data: {
      dataType: {
        name: "",
        description: "",
        fields: []
      } as DataType
    },
    analysis: {
      settings: {
        aggregations: [] as string[],
        filters: [] as string[],
        calculations: [] as string[],
        groupBy: [] as string[],
        sortBy: null as string | null,
        limit: 100
      }
    },
    template: {
      selectedLayout: null as any,
      reportTemplate: {
        name: "",
        description: "",
        pages: [{
          id: `page-${Date.now()}`,
          name: "Page 1",
          description: "",
          dataElements: []
        }]
      } as ReportTemplate
    },
    review: {
      exportOptions: {
        format: "pdf",
        pages: [],
        includeFooter: true,
        paperSize: "a4",
        orientation: "portrait",
        tableOfContents: true,
        pageNumbers: true
      }
    }
  });

  // Validate the current step
  const validateCurrentStep = () => {
    const currentStepId = steps[currentStepIndex].id;
    const newErrors: Record<string, string> = {};
    
    switch (currentStepId) {
      case "basics":
        const { title, startDate, endDate } = reportData.basics;
        
        if (!title) {
          newErrors.title = "Report title is required";
        }
        
        if (!startDate) {
          newErrors.startDate = "Start date is required";
        }
        
        if (!endDate) {
          newErrors.endDate = "End date is required";
        }
        
        if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
          newErrors.dateRange = "End date must be after start date";
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
        
      // Other step validations will be implemented later
      default:
        return true;
    }
  };

  // Handle next step navigation
  const handleNextStep = () => {
    if (validateCurrentStep()) {
      const currentStepId = steps[currentStepIndex].id;
      
      // Mark current step as completed if not already
      if (!completedSteps.includes(currentStepId)) {
        setCompletedSteps([...completedSteps, currentStepId]);
      }
      
      // Move to next step
      if (currentStepIndex < steps.length - 1) {
        setCurrentStepIndex(currentStepIndex + 1);
      }
    }
  };

  // Handle previous step navigation
  const handlePreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateCurrentStep()) {
      // In a real implementation, this would submit the report data to the server
      console.log("Submitting report:", reportData);
      alert("Report submitted successfully!");
    }
  };

  // Render the current step content
  const renderStepContent = () => {
    const currentStepId = steps[currentStepIndex].id;
    
    switch (currentStepId) {
      case "basics":
        return renderBasicsStep();
      case "data":
        return renderDataInputStep();
      case "analysis":
        return renderAnalysisStep();
      case "template":
        return renderTemplateStep();
      case "review":
        return renderReviewStep();
      default:
        return null;
    }
  };

  // Render the Review & Generate step
  const renderReviewStep = () => {
    const { dataType } = reportData.data;
    const { settings } = reportData.analysis;
    const { selectedLayout, reportTemplate } = reportData.template;
    const { exportOptions } = reportData.review;
    
    // Sample data for preview
    const sampleData = {
      // Generate sample data based on the data type
      ...dataType.fields.reduce((acc, field) => {
        // Generate sample values based on field type
        let value;
        switch (field.type) {
          case 'text':
          case 'select':
            value = field.label;
            break;
          case 'number':
          case 'integer':
          case 'decimal':
            value = Math.floor(Math.random() * 100);
            break;
          case 'boolean':
            value = Math.random() > 0.5;
            break;
          case 'date':
            value = new Date().toISOString().split('T')[0];
            break;
          default:
            value = null;
        }
        return { ...acc, [field.name]: value };
      }, {})
    };
    
    // Sample report pages for export options
    const reportPages = selectedLayout ? 
      selectedLayout.components.map((component: any, index: number) => ({
        id: `page-${index}`,
        name: component.title || `Component ${index + 1}`,
        description: `Sample component ${index + 1}`
      })) : 
      reportTemplate.pages.map(page => ({
        id: page.id,
        name: page.name,
        description: page.description
      }));
    
    // Handle export
    const handleExport = async (config: any) => {
      console.log("Exporting report with config:", config);
      // In a real implementation, this would call an API to generate the report
      
      // For demo purposes, just show a success message
      alert(`Report exported successfully in ${config.format.toUpperCase()} format!`);
    };
    
    return (
      <div>
        <h3 className="text-lg font-medium mb-4">Review & Generate Report</h3>
        <p className="text-gray-500 mb-6">
          Preview your report and configure export options.
        </p>
        
        <div className="space-y-8">
          {/* Report Preview */}
          <div>
            <h4 className="text-md font-medium mb-3 flex items-center">
              <Layout className="h-4 w-4 mr-2" />
              Report Preview
            </h4>
            
            <div className="border rounded-md overflow-hidden">
              {selectedLayout ? (
                <LayoutRenderer
                  layout={selectedLayout}
                  data={sampleData}
                  dataType={dataType}
                />
              ) : (
                <div className="p-8 text-center bg-gray-50">
                  <Layout className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">
                    No layout selected. Please go back to the Template step and select a layout.
                  </p>
                </div>
              )}
            </div>
          </div>
          
          {/* Export Options */}
          <div>
            <h4 className="text-md font-medium mb-3 flex items-center">
              <Download className="h-4 w-4 mr-2" />
              Export Options
            </h4>
            
            <div className="border rounded-md p-4">
              <ExportOptions
                reportId={`report-${Date.now()}`}
                reportName={reportData.basics.title || "Untitled Report"}
                reportPages={reportPages}
                onExport={handleExport}
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render the Template Selection step
  const renderTemplateStep = () => {
    const { dataType } = reportData.data;
    const { selectedLayout, reportTemplate } = reportData.template;
    
    // Sample data for layout suggestion
    const sampleData = {
      // Generate sample data based on the data type
      ...dataType.fields.reduce((acc, field) => {
        // Generate sample values based on field type
        let value;
        switch (field.type) {
          case 'text':
          case 'select':
            value = field.label;
            break;
          case 'number':
          case 'integer':
          case 'decimal':
            value = Math.floor(Math.random() * 100);
            break;
          case 'boolean':
            value = Math.random() > 0.5;
            break;
          case 'date':
            value = new Date().toISOString().split('T')[0];
            break;
          default:
            value = null;
        }
        return { ...acc, [field.name]: value };
      }, {})
    };

    return (
      <div>
        <h3 className="text-lg font-medium mb-4">Select Report Template</h3>
        <p className="text-gray-500 mb-6">
          Choose a template for your report or create a custom one.
        </p>
        
        <div className="mb-6">
          <div className="flex border-b">
            <button
              className={`px-4 py-2 ${
                templateTab === 'suggestion' 
                  ? 'border-b-2 border-tertiary text-primary' 
                  : 'text-gray-500'
              }`}
              onClick={() => setTemplateTab('suggestion')}
            >
              Suggested Layouts
            </button>
            <button
              className={`px-4 py-2 ${
                templateTab === 'custom' 
                  ? 'border-b-2 border-tertiary text-primary' 
                  : 'text-gray-500'
              }`}
              onClick={() => setTemplateTab('custom')}
            >
              Custom Template
            </button>
          </div>
        </div>
        
        {templateTab === 'suggestion' ? (
          <LayoutSuggestion
            data={sampleData}
            dataType={dataType}
            onSelectLayout={(layout) => {
              setReportData({
                ...reportData,
                template: {
                  ...reportData.template,
                  selectedLayout: layout
                }
              });
              
              // Mark this step as valid and move to next step
              if (!completedSteps.includes("template")) {
                setCompletedSteps([...completedSteps, "template"]);
              }
              
              if (currentStepIndex < steps.length - 1) {
                setCurrentStepIndex(currentStepIndex + 1);
              }
            }}
          />
        ) : (
          <TemplateBuilder
            initialTemplate={reportTemplate}
            dataTypeId={dataType.id}
            onSave={(template) => {
              setReportData({
                ...reportData,
                template: {
                  ...reportData.template,
                  reportTemplate: template
                }
              });
              
              // Mark this step as valid and move to next step
              if (!completedSteps.includes("template")) {
                setCompletedSteps([...completedSteps, "template"]);
              }
              
              if (currentStepIndex < steps.length - 1) {
                setCurrentStepIndex(currentStepIndex + 1);
              }
            }}
            onCancel={() => {
              // If user cancels, stay on this tab
            }}
          />
        )}
      </div>
    );
  };

  // Render the Analysis Settings step
  const renderAnalysisStep = () => {
    const { dataType } = reportData.data;
    const { settings } = reportData.analysis;
    const numericFields = dataType.fields.filter(f => 
      ['number', 'integer', 'decimal'].includes(f.type)
    );
    const categoricalFields = dataType.fields.filter(f => 
      ['text', 'select', 'multiSelect'].includes(f.type)
    );
    const dateFields = dataType.fields.filter(f => 
      ['date', 'datetime', 'time'].includes(f.type)
    );

    // Handle aggregation selection
    const handleAggregationChange = (field: string, type: string) => {
      const aggregation = `${type}(${field})`;
      const currentAggregations = [...settings.aggregations];
      
      const index = currentAggregations.findIndex(agg => 
        agg.startsWith(`${type}(${field})`)
      );
      
      if (index >= 0) {
        currentAggregations.splice(index, 1);
      } else {
        currentAggregations.push(aggregation);
      }
      
      setReportData({
        ...reportData,
        analysis: {
          ...reportData.analysis,
          settings: {
            ...settings,
            aggregations: currentAggregations
          }
        }
      });
    };

    // Handle group by selection
    const handleGroupByChange = (field: string) => {
      const currentGroupBy = [...settings.groupBy];
      
      if (currentGroupBy.includes(field)) {
        const index = currentGroupBy.indexOf(field);
        currentGroupBy.splice(index, 1);
      } else {
        currentGroupBy.push(field);
      }
      
      setReportData({
        ...reportData,
        analysis: {
          ...reportData.analysis,
          settings: {
            ...settings,
            groupBy: currentGroupBy
          }
        }
      });
    };

    // Handle sort by selection
    const handleSortByChange = (field: string) => {
      const currentSortBy = settings.sortBy;
      
      if (currentSortBy === field) {
        // Toggle between ascending and descending
        setReportData({
          ...reportData,
          analysis: {
            ...reportData.analysis,
            settings: {
              ...settings,
              sortBy: `${field} DESC`
            }
          }
        });
      } else if (currentSortBy === `${field} DESC`) {
        // Clear sort
        setReportData({
          ...reportData,
          analysis: {
            ...reportData.analysis,
            settings: {
              ...settings,
              sortBy: null
            }
          }
        });
      } else {
        // Set ascending sort
        setReportData({
          ...reportData,
          analysis: {
            ...reportData.analysis,
            settings: {
              ...settings,
              sortBy: field
            }
          }
        });
      }
    };

    // Handle limit change
    const handleLimitChange = (limit: string) => {
      setReportData({
        ...reportData,
        analysis: {
          ...reportData.analysis,
          settings: {
            ...settings,
            limit: parseInt(limit) || 100
          }
        }
      });
    };

    // Save analysis settings and move to next step
    const handleSaveAnalysisSettings = () => {
      // Mark this step as valid and move to next step
      if (!completedSteps.includes("analysis")) {
        setCompletedSteps([...completedSteps, "analysis"]);
      }
      
      if (currentStepIndex < steps.length - 1) {
        setCurrentStepIndex(currentStepIndex + 1);
      }
    };

    return (
      <div>
        <h3 className="text-lg font-medium mb-4">Configure Analysis Settings</h3>
        <p className="text-gray-500 mb-6">
          Customize how your data will be analyzed and presented in the report.
        </p>
        
        <div className="space-y-8">
          {/* Aggregations */}
          <div>
            <h4 className="text-md font-medium mb-3 flex items-center">
              <Calculator className="h-4 w-4 mr-2" />
              Aggregations
            </h4>
            <p className="text-sm text-gray-500 mb-4">
              Select aggregation functions to apply to numeric fields.
            </p>
            
            {numericFields.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {numericFields.map(field => (
                  <div key={field.id} className="border rounded-md p-3">
                    <p className="font-medium mb-2">{field.label}</p>
                    <div className="space-y-2">
                      {['SUM', 'AVG', 'MIN', 'MAX', 'COUNT'].map(aggType => (
                        <div key={`${field.name}-${aggType}`} className="flex items-center">
                          <Checkbox
                            id={`${field.name}-${aggType}`}
                            checked={settings.aggregations.includes(`${aggType}(${field.name})`)}
                            onCheckedChange={() => handleAggregationChange(field.name, aggType)}
                          />
                          <label
                            htmlFor={`${field.name}-${aggType}`}
                            className="ml-2 text-sm font-medium"
                          >
                            {aggType}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">
                No numeric fields available for aggregation.
              </p>
            )}
          </div>
          
          {/* Group By */}
          <div>
            <h4 className="text-md font-medium mb-3 flex items-center">
              <BarChart className="h-4 w-4 mr-2" />
              Group By
            </h4>
            <p className="text-sm text-gray-500 mb-4">
              Select fields to group your data by.
            </p>
            
            {categoricalFields.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {categoricalFields.map(field => (
                  <div key={field.id} className="flex items-center">
                    <Checkbox
                      id={`groupby-${field.name}`}
                      checked={settings.groupBy.includes(field.name)}
                      onCheckedChange={() => handleGroupByChange(field.name)}
                    />
                    <label
                      htmlFor={`groupby-${field.name}`}
                      className="ml-2 text-sm font-medium"
                    >
                      {field.label}
                    </label>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">
                No categorical fields available for grouping.
              </p>
            )}
          </div>
          
          {/* Sort By */}
          <div>
            <h4 className="text-md font-medium mb-3 flex items-center">
              <LineChart className="h-4 w-4 mr-2" />
              Sort By
            </h4>
            <p className="text-sm text-gray-500 mb-4">
              Select a field to sort your data by.
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {dataType.fields.map(field => {
                const isSorted = settings.sortBy === field.name;
                const isSortedDesc = settings.sortBy === `${field.name} DESC`;
                
                return (
                  <button
                    key={field.id}
                    className={`px-3 py-2 text-sm border rounded-md text-left ${
                      isSorted || isSortedDesc ? 'border-tertiary bg-primary' : 'border-gray-300'
                    }`}
                    onClick={() => handleSortByChange(field.name)}
                  >
                    {field.label}
                    {isSorted && <span className="ml-2">↑</span>}
                    {isSortedDesc && <span className="ml-2">↓</span>}
                  </button>
                );
              })}
            </div>
          </div>
          
          {/* Limit */}
          <div>
            <h4 className="text-md font-medium mb-3 flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              Result Limit
            </h4>
            <p className="text-sm text-gray-500 mb-4">
              Limit the number of results to display.
            </p>
            
            <div className="w-full md:w-1/3">
              <input
                type="number"
                min="1"
                max="1000"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                value={settings.limit}
                onChange={(e) => handleLimitChange(e.target.value)}
              />
            </div>
          </div>
        </div>
        
        <div className="mt-8 flex justify-end">
          <button
            type="button"
            onClick={handleSaveAnalysisSettings}
            className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary/70 inline-flex items-center"
          >
            <Save className="h-4 w-4 mr-1" />
            Save Analysis Settings
          </button>
        </div>
      </div>
    );
  };

  // Render the Data Input step
  const renderDataInputStep = () => {
    return (
      <div>
        <h3 className="text-lg font-medium mb-4">Define Your Data Structure</h3>
        <p className="text-gray-500 mb-6">
          Create a data structure for your report by defining fields and their types.
          This will determine what data can be included in your report.
        </p>
        
        <DataTypeBuilder
          initialDataType={reportData.data.dataType}
          onSave={(dataType) => {
            setReportData({
              ...reportData,
              data: { ...reportData.data, dataType }
            });
            
            // Mark this step as valid and move to next step
            if (!completedSteps.includes("data")) {
              setCompletedSteps([...completedSteps, "data"]);
            }
            
            if (currentStepIndex < steps.length - 1) {
              setCurrentStepIndex(currentStepIndex + 1);
            }
          }}
          onCancel={() => {
            // If user cancels and there's no data type defined yet, go back to previous step
            if (reportData.data.dataType.fields.length === 0 && currentStepIndex > 0) {
              setCurrentStepIndex(currentStepIndex - 1);
            }
          }}
        />
      </div>
    );
  };

  // Render the Basics step
  const renderBasicsStep = () => {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            Report Title
          </label>
          <input
            type="text"
            className={`w-full px-3 py-2 border rounded-md ${
              errors.title ? "border-red-500" : "border-gray-300"
            }`}
            placeholder="Enter report title"
            value={reportData.basics.title}
            onChange={(e) => setReportData({
              ...reportData,
              basics: { ...reportData.basics, title: e.target.value }
            })}
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-500">{errors.title}</p>
          )}
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            Description
          </label>
          <textarea
            className="w-full px-3 py-2 border rounded-md"
            rows={4}
            placeholder="Enter report description"
            value={reportData.basics.description}
            onChange={(e) => setReportData({
              ...reportData,
              basics: { ...reportData.basics, description: e.target.value }
            })}
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">
            Time Period
          </label>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <input
                type="date"
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.startDate ? "border-red-500" : "border-gray-300"
                }`}
                value={reportData.basics.startDate}
                onChange={(e) => setReportData({
                  ...reportData,
                  basics: { ...reportData.basics, startDate: e.target.value }
                })}
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-500">{errors.startDate}</p>
              )}
            </div>
            <div>
              <input
                type="date"
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.endDate ? "border-red-500" : "border-gray-300"
                }`}
                value={reportData.basics.endDate}
                onChange={(e) => setReportData({
                  ...reportData,
                  basics: { ...reportData.basics, endDate: e.target.value }
                })}
              />
              {errors.endDate && (
                <p className="mt-1 text-sm text-red-500">{errors.endDate}</p>
              )}
            </div>
          </div>
          {errors.dateRange && (
            <p className="mt-1 text-sm text-red-500">{errors.dateRange}</p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <h1 className="text-3xl font-bold">Create New Report</h1>
      
      {/* Progress Steps */}
      <div className="mt-8">
        <Stepper activeStep={currentStepIndex}>
          {steps.map((step, index) => (
            <Step 
              key={step.id} 
              completed={completedSteps.includes(step.id)}
            >
              <StepLabel>{step.title}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </div>

      {/* Form Content */}
      <div className="mt-8">
        <div className="rounded-lg border p-6">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="mt-6 flex justify-between">
          <button
            className="px-4 py-2 text-sm border rounded-md hover:bg-accent"
            onClick={handlePreviousStep}
            disabled={currentStepIndex === 0}
          >
            <ChevronLeft className="mr-2 h-4 w-4 inline" />
            Back
          </button>
          
          {currentStepIndex === steps.length - 1 ? (
            <button
              className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 inline-flex items-center"
              onClick={handleSubmit}
            >
              <Save className="mr-2 h-4 w-4" />
              Generate Report
            </button>
          ) : (
            <button
              className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 inline-flex items-center"
              onClick={handleNextStep}
            >
              Next Step
              <ChevronRight className="ml-2 h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
