using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Interface for LLM client implementations
    /// </summary>
    public interface ILlmClient
    {
        /// <summary>
        /// Generates HTML template content based on the provided prompt
        /// </summary>
        /// <param name="prompt">The XML prompt containing instructions and data</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The generated HTML template</returns>
        Task<string> GenerateHtmlTemplateAsync(string prompt, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves metrics about the LLM client usage
        /// </summary>
        /// <returns>LLM usage metrics</returns>
        Task<LlmMetrics> GetMetricsAsync();
    }
}
