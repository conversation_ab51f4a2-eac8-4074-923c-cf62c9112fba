using System;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    public class Report : FullAuditedMultiTenantEntity<Guid>
    {
        public string ReportNumber { get; set; } = string.Empty; // e.g., "CSR-2025-001"
        public Guid ClientId { get; set; } // Foreign key to Client
        public string ClientName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty; // reportName in JSON
        public string Category { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;

        // Navigation property
        public virtual Client? Client { get; set; }

        public Report() : base() { }

        public Report(
            Guid id,
            string reportNumber,
            Guid clientId,
            string clientName,
            string name,
            string category,
            int slideCount,
            string status,
            string author)
            : base(id)
        {
            ReportNumber = reportNumber;
            ClientId = clientId;
            ClientName = clientName;
            Name = name;
            Category = category;
            SlideCount = slideCount;
            Status = status;
            Author = author;
        }

        public void UpdateDetails(
            string name,
            string category,
            int slideCount,
            string status,
            string author)
        {
            Name = name;
            Category = category;
            SlideCount = slideCount;
            Status = status;
            Author = author;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetStatus(string status)
        {
            Status = status;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void UpdateSlideCount(int count)
        {
            SlideCount = count;
            // LastModificationTime and LastModifierId will be set by DbContext
        }
    }
}
