using System;
using System.Collections.Generic;
using System.Text.Json;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    public class Template : FullAuditedMultiTenantEntity<Guid>
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string ThumbnailUrl { get; set; } = string.Empty;
        
        // Store tags as a JSON array string
        private string _tags = "[]";
        public string Tags
        {
            get => _tags;
            set => _tags = string.IsNullOrEmpty(value) ? "[]" : value;
        }

        // Store sections as a JSON array string
        private string _sections = "[]";
        public string Sections
        {
            get => _sections;
            set => _sections = string.IsNullOrEmpty(value) ? "[]" : value;
        }

        // Store fields as a JSON array string
        private string _fields = "[]";
        public string Fields
        {
            get => _fields;
            set => _fields = string.IsNullOrEmpty(value) ? "[]" : value;
        }

        public Template() : base() { }

        public Template(
            Guid id,
            string name,
            string description,
            string category,
            string thumbnailUrl,
            IEnumerable<string>? tags = null,
            IEnumerable<TemplateSection>? sections = null,
            IEnumerable<TemplateField>? fields = null)
            : base(id)
        {
            Name = name;
            Description = description;
            Category = category;
            ThumbnailUrl = thumbnailUrl;
            
            if (tags != null)
                SetTags(tags);
            
            if (sections != null)
                SetSections(sections);
            
            if (fields != null)
                SetFields(fields);
        }

        public void UpdateDetails(
            string name,
            string description,
            string category,
            string thumbnailUrl)
        {
            Name = name;
            Description = description;
            Category = category;
            ThumbnailUrl = thumbnailUrl;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetTags(IEnumerable<string> tags)
        {
            Tags = JsonSerializer.Serialize(tags);
        }

        public IEnumerable<string> GetTags()
        {
            return JsonSerializer.Deserialize<IEnumerable<string>>(Tags) ?? Array.Empty<string>();
        }

        public void SetSections(IEnumerable<TemplateSection> sections)
        {
            Sections = JsonSerializer.Serialize(sections);
        }

        public IEnumerable<TemplateSection> GetSections()
        {
            return JsonSerializer.Deserialize<IEnumerable<TemplateSection>>(Sections) ?? Array.Empty<TemplateSection>();
        }

        public void SetFields(IEnumerable<TemplateField> fields)
        {
            Fields = JsonSerializer.Serialize(fields);
        }

        public IEnumerable<TemplateField> GetFields()
        {
            return JsonSerializer.Deserialize<IEnumerable<TemplateField>>(Fields) ?? Array.Empty<TemplateField>();
        }
    }

    public class TemplateSection
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
    }

    public class TemplateField
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public JsonDocument? Config { get; set; }
    }
}
