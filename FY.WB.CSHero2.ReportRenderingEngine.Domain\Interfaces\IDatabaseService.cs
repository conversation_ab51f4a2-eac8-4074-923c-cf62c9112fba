using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Interface for database access to retrieve templates, data, and prompts
    /// </summary>
    public interface IDatabaseService
    {
        /// <summary>
        /// Retrieves template metadata for a specific document
        /// </summary>
        /// <param name="documentId">The unique identifier of the document</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The template metadata</returns>
        Task<DocumentTemplateMetadata> GetTemplateMetadataAsync(Guid documentId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves template data for a specific document
        /// </summary>
        /// <param name="documentId">The unique identifier of the document</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A dictionary containing template data</returns>
        Task<Dictionary<string, object>> GetTemplateDataAsync(Guid documentId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves existing HTML content for a specific document, if available
        /// </summary>
        /// <param name="documentId">The unique identifier of the document</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>Existing HTML content or null if not available</returns>
        Task<string?> GetExistingHtmlAsync(Guid documentId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves a prompt by its key
        /// </summary>
        /// <param name="key">The unique key for the prompt</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>The prompt content</returns>
        Task<string> GetPromptByKeyAsync(string key, CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves a prompt with the specified key
        /// </summary>
        /// <param name="key">The unique key for the prompt</param>
        /// <param name="prompt">The prompt content to save</param>
        /// <param name="cancellationToken">Optional cancellation token</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task SavePromptAsync(string key, string prompt, CancellationToken cancellationToken = default);
    }
}
